# User Stories

## Story 1: Client launches robot into the world

**As the client** I want to launch my robot into the world **So that** I can start to play the game

### Scenario 1: Launch command is valid
- **Given** the world is set up successfully
- **And** the client successfully connects to the server
- **When** the client sends the launch command to the world
- **Then** the world should launch the robot
- **And** the client should receive the required response

### Scenario 2: Launch command is not valid
- **Given** the world is set up successfully
- **And** the client successfully connects to the server
- **When** the client sends the launch command to the world
- **And** the command does not follow the launch command requirements
- **Then** the world should not launch the robot
- **And** the client should receive the required response

### Scenario 3: The world already has a robot with same name
- **Given** the world is set up successfully
- **And** the client successfully connects to the server
- **When** the client sends the launch command to the world
- **And** the command does follow the launch command requirements
- **And** the world already has a robot with the same name
- **Then** the world should not launch the robot
- **And** the client should receive the required response

### Scenario 4: The world already has no space
- **Given** the world is set up successfully
- **And** the client successfully connects to the server
- **When** the client sends the launch command to the world
- **And** the command does follow the launch command requirements
- **And** the world already has reached its full capacity
- **Then** the world should not launch the robot
- **And** the client should receive the required response

## Story 2: Get Robot State

**As a player** I want to check the robot's current state **so that** I can make informed decisions.

### Scenario a: State is requested for a valid robot
- **Given** a robot is active in the world
- **When** I send the state command
- **Then** I should receive a JSON response showing:
  - position [x, y]
  - type (sniper, soldier, hitbot)
  - direction (NORTH, EAST, etc.)
  - shields remaining
  - shots remaining
  - status (e.g., NORMAL, DEAD, RELOAD)

### Scenario b: State requested for a non-existent robot
- **Given** no robot with that name exists in the world
- **When** I send a state command with an unknown name
- **Then** I should receive an error message indicating the robot does not exist

## Story 3: Look

**As a player** I want to look around the robot's position in the online robot world **so that** I can see what is nearby and plan my actions.

### Scenario a: The world is empty
- **Given** that I am connected to a running Robot Worlds server
- **And** a robot with the name "HAL" exists in the world
- **And** the world contains no other robots or obstacles
- **When** I send a valid look request for "HAL"
- **Then** I should get a valid response from the server
- **And** the response should indicate that no objects are visible around the robot

## Story 4: Client Connection and Communication

**As a player** I want to connect to the robot world server through a client application **so that** I can interact with the game world and control my robot.

### Scenario a: Successful client connection
- **Given** the server is running on the configured host and port
- **When** I start the client application
- **Then** the client should connect to the server successfully
- **And** I should see a connection confirmation message
- **And** I should be prompted to launch a robot

### Scenario b: Client robot launch with valid input
- **Given** the client is connected to the server
- **When** I enter "launch soldier MyBot"
- **Then** the client should send a properly formatted JSON launch request
- **And** I should receive a success response from the server
- **And** the client should display the robot's initial state
- **And** I should be prompted for the next command

## Story 5: World Management and State

**As a system** I want to manage the world environment and robot interactions **so that** the game provides a consistent and interactive playing field.

### Scenario a: World initialization
- **Given** the world configuration is loaded
- **When** a new world is created with GUI enabled
- **Then** the world should set boundaries from (0,0) to (HEIGHT-1, WIDTH-1)
- **And** obstacles should be loaded from the maze configuration
- **And** an empty robot list should be initialized
- **And** a WorldGUI should be created and displayed

### Scenario b: Robot addition to world
- **Given** a world exists with available space
- **When** a new robot is added to the world
- **Then** the robot should be added to the robot list
- **And** the robot should be assigned a valid position
- **And** if GUI is enabled, the GUI should be updated to show the new robot
- **And** the robot count should increase by one

### Scenario c: Position validation for movement
- **Given** a robot is at position (10, 10)
- **And** the robot wants to move to position (12, 10)
- **When** the world validates the new position
- **Then** the world should check for other robots at that position
- **And** the world should check for blocking obstacles
- **And** the world should return true if the position is valid

### Scenario d: Robot death from bottomless pit
- **Given** a robot is moving toward a bottomless pit
- **When** the robot's path intersects with the pit
- **Then** the robot status should be set to DEAD
- **And** the movement should return DIED_FELL_IN_PIT
- **And** if GUI is enabled, the GUI should be updated to show the robot's death

## Story 6: Graphical World Visualization

**As a player** I want to see a visual representation of the robot world **so that** I can understand the game state and robot positions.

### Scenario a: GUI initialization
- **Given** a world is created with GUI enabled
- **When** the WorldGUI is initialized
- **Then** a window should open showing the world grid
- **And** the window should display world size information
- **And** a legend should show obstacle types with their icons
- **And** the window should be titled "Toy Robot"
- **And** the window should be visible and stay on top

### Scenario b: Robot avatar assignment
- **Given** the GUI is initialized
- **When** robots are added to the world
- **Then** each robot should be assigned a unique avatar from available images
- **And** the avatar should be displayed at the robot's position
- **And** hovering over the robot should show its name as a tooltip
- **And** the robot list should be updated to show all robot names

## Story 7: ASCII World Representation

**As a system administrator** I want to generate a text-based representation of the world **so that** I can export, save, and analyze the world state.

### Scenario a: ASCII world generation
- **Given** a world contains robots and obstacles
- **When** the ASCII world representation is generated
- **Then** a text grid should be created matching the world dimensions
- **And** the grid should be surrounded by borders (| and -)
- **And** empty spaces should be represented by spaces
- **And** a legend should explain all symbols used

### Scenario b: ASCII file output
- **Given** the ASCII world representation is generated
- **When** the printToTxt method is called
- **Then** the representation should be written to "Ascii-World.txt"
- **And** the file should contain the complete grid with borders
- **And** the file should include the legend at the top
- **And** a success message should be displayed: "Successfully wrote world to Ascii-World.txt"

## Story 8: Combat System and Weapon Firing

**As a player** I want to fire my robot's weapon at enemies **so that** I can engage in combat and eliminate opposing robots.

### Scenario a: Successful hit on target robot
- **Given** a robot "Shooter" is at position (5, 5) facing EAST with 3 shots
- **And** another robot "Target" is at position (8, 5) with 2 shields
- **And** there are no obstacles between them
- **And** the target is within bullet range
- **When** "Shooter" fires its weapon
- **Then** the hit detection should calculate the target is in range
- **And** "Target" should be hit and lose 1 shield
- **And** the response should indicate "Hit"
- **And** the response should include distance and target robot name
- **And** the target's updated state should be included
- **And** "Shooter" should have 2 shots remaining

### Scenario b: Shot blocked by mountain obstacle
- **Given** a robot "Shooter" is at position (5, 5) facing EAST with 3 shots
- **And** there is a mountain obstacle at position (6, 5)
- **And** another robot "Target" is at position (8, 5)
- **When** "Shooter" fires its weapon
- **Then** the system should detect the mountain in the bullet's path
- **And** the shot should be blocked before reaching the target
- **And** the response should indicate "Miss"
- **And** "Target" should not be affected
- **And** "Shooter" should have 2 shots remaining

### Scenario c: Out of ammunition
- **Given** a robot "Shooter" is at position (5, 5) with 0 shots
- **And** another robot "Target" is at position (8, 5)
- **When** "Shooter" attempts to fire
- **Then** the system should check the ammunition count
- **And** the response should indicate "Out of ammo."
- **And** no shot should be fired
- **And** no robots should be affected
- **And** the ammunition count should remain at 0

### Scenario d: Target robot dies from shot
- **Given** a robot "Shooter" is at position (5, 5) facing EAST with 3 shots
- **And** another robot "Target" is at position (8, 5) with 0 shields
- **When** "Shooter" fires its weapon
- **Then** "Target" should be hit
- **And** "Target" should die due to having no shields
- **And** "Target" status should be set to DEAD
- **And** the server should send a quit message to "Target"
- **And** the response should indicate "Hit" with target's final state

### Story 9: Look

## Scenario a: Seeing an obstacle
-**Given** a world of size 2x2
-**and** the world has an obstacle at coordinate [0,1]
-**and** I have successfully launched a robot into the world
-**When** I ask the robot to look
-**Then** I should get an response back with an object of type OBSTACLE at a distance of 1 step in its direction

## Scenario b: Seeing robots and an obstacle
-**Given** a world of size 2x2
-**and** the world has an obstacle at coordinate [0,1]
-**and** I have successfully launched multiple robots into the world
-**When** I ask the first robot to look
-**Then** I should get a response back with
-**one** object being an OBSTACLE that is one step away
-**and** three objects should be ROBOTs that is one step away

## Story 10: Save the World

**As a RobotWorld creator** I want to save my world to a database using a SAVE command **So that** I can reuse it later.

### Scenario a: Save world with name
- **Given** the server is running
- **When** I enter "SAVE myworld"
- **Then** the world should be saved as "myworld"

### Scenario b: Save world without name
- **Given** the server is running
- **When** I enter "SAVE"
- **Then** I should be prompted for a world name

## Story 11: Restore the World

**As a player** I want to load a saved world from the database **So that** I can play in previously created worlds.

### Scenario a: Restore by name
- **Given** a world "myworld" exists in the database
- **When** I enter "RESTORE myworld"
- **Then** the world should be loaded

### Scenario b: List worlds
- **Given** worlds exist in the database
- **When** I enter "RESTORE list"
- **Then** I should see all available worlds

## Story 12: Named Worlds

**As an admin** I want to save multiple worlds with different names **So that** I can manage many world configurations.

### Scenario a: Save unique world
- **Given** no world named "test" exists
- **When** I enter "SAVE test"
- **Then** the world should be saved as "test"

### Scenario b: Handle duplicate names
- **Given** a world "test" already exists
- **When** I enter "SAVE test"
- **Then** I should be asked if I want to overwrite it

