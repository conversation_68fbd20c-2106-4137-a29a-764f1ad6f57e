package za.co.wethinkcode.robots.Database;



import za.co.wethinkcode.robots.maze.Maze;
import za.co.wethinkcode.robots.obstacle.Obstacle;
import za.co.wethinkcode.robots.obstacle.ObstacleType;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.world.World;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * RecipeDbDemo shows how to read/write/update/delete data using the example Brewery Database.
 */
public class SaveDatabase
{
    public static final String DISK_DB_URL_PREFIX = "jdbc:sqlite:world.db";
    public MultiServers server;
//    public static final String SEPARATOR = "\t";

//    public static void main( String[] args ) {
//        final SaveDatabase app = new SaveDatabase( args );
//    }

   private String dbUrl;
//    public String getDbUrl(){return dbUrl;}

    public SaveDatabase() throws SQLException {
        this.dbUrl = DISK_DB_URL_PREFIX;
        initializeDatabase();
    }
//        processCmdLineArgs( args );
    public void createConnection(){
        try( Connection connection = DriverManager.getConnection( dbUrl )){ // <1>Other ways to get a Connection
            useTheDb( connection );
        }catch( SQLException e ){
            throw new RuntimeException( e );
        }
    }

    public Connection getConnection() throws SQLException {
        return DriverManager.getConnection(dbUrl);
    }

    private void initializeDatabase() throws SQLException {
        try (Connection connection = getConnection()) {
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate("CREATE TABLE IF NOT EXISTS world (id INTEGER PRIMARY KEY AUTOINCREMENT, size INTEGER, name TEXT UNIQUE)");

                // Add name column if it doesn't exist
                try {
                    stmt.executeUpdate("ALTER TABLE world ADD COLUMN name TEXT UNIQUE");
                } catch (SQLException e) {
                    // Column already exists, ignore
                }
            }
        }
    }


    public void useTheDb(final Connection connection) throws SQLException {
        int worldID = createData(connection);
        World world = MultiServers.getServer().getWorld();
        obstacleData(connection, world, worldID);
        restoreWorld(connection,worldID);
    }

    public void saveWorldWithName(String worldName) throws SQLException {
        try (Connection connection = getConnection()) {
            if (worldExists(connection, worldName)) {
                handleDuplicateWorld(connection, worldName);
            } else {
                int worldID = createNamedWorld(connection, worldName);
                World world = MultiServers.getServer().getWorld();
                obstacleData(connection, world, worldID);
                System.out.println("World '" + worldName + "' saved.");
            }
        }
    }

    private boolean worldExists(Connection connection, String worldName) throws SQLException {
        PreparedStatement stmt = connection.prepareStatement("SELECT COUNT(*) FROM world WHERE name = ?");
        stmt.setString(1, worldName);
        ResultSet rs = stmt.executeQuery();
        boolean exists = rs.next() && rs.getInt(1) > 0;
        rs.close();
        stmt.close();
        return exists;
    }

    /**
     * Handle duplicate world name scenario
     */
    private void handleDuplicateWorld(Connection connection, String worldName) throws SQLException {
        System.out.println("World '" + worldName + "' already exists.");
        System.out.print("Overwrite? (y/n): ");

        Scanner scanner = new Scanner(System.in);
        String choice = scanner.nextLine().toLowerCase().trim();

        if (choice.equals("y") || choice.equals("yes")) {
            overwriteWorld(connection, worldName);
            System.out.println("World '" + worldName + "' overwritten.");
        } else {
            System.out.println("Save cancelled.");
        }
    }

    private void overwriteWorld(Connection connection, String worldName) throws SQLException {
        PreparedStatement deleteStmt = connection.prepareStatement(
                "DELETE FROM obstacles WHERE worldID = (SELECT id FROM world WHERE name = ?)");
        deleteStmt.setString(1, worldName);
        deleteStmt.executeUpdate();
        deleteStmt.close();

        PreparedStatement updateStmt = connection.prepareStatement(
                "UPDATE world SET size = ? WHERE name = ?");
        updateStmt.setInt(1, this.server.getSize());
        updateStmt.setString(2, worldName);
        updateStmt.executeUpdate();
        updateStmt.close();

        PreparedStatement selectStmt = connection.prepareStatement("SELECT id FROM world WHERE name = ?");
        selectStmt.setString(1, worldName);
        ResultSet rs = selectStmt.executeQuery();
        if (rs.next()) {
            int worldID = rs.getInt("id");
            World world = MultiServers.getServer().getWorld();
            obstacleData(connection, world, worldID);
        }
        rs.close();
        selectStmt.close();
    }


    private int createData(final Connection connection)
            throws SQLException {

        try (final PreparedStatement statement = connection.prepareStatement("INSERT INTO world ( size) VALUES (?)")) {
            statement.setInt(1, this.server.getSize());
            statement.executeUpdate();

            try (ResultSet keys = statement.getGeneratedKeys()) {
                if (keys.next()) {
                    return keys.getInt(1);
                } else {
                    throw new SQLException("No world ID returned.");
                }
            }
        }
    }

    private int createNamedWorld(final Connection connection, String worldName) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(
                "INSERT INTO world (size, name) VALUES (?, ?)", Statement.RETURN_GENERATED_KEYS);
        statement.setInt(1, this.server.getSize());
        statement.setString(2, worldName);
        statement.executeUpdate();

        ResultSet keys = statement.getGeneratedKeys();
        if (keys.next()) {
            int id = keys.getInt(1);
            keys.close();
            statement.close();
            return id;
        } else {
            keys.close();
            statement.close();
            throw new SQLException("No world ID returned.");
        }
    }

    private void obstacleData(final Connection connection, World world, int worldID) throws SQLException {
        String obstacleString = this.server.getObstacle(); // e.g. "1,2;3,4"

        if (obstacleString.equalsIgnoreCase("none")) {
            System.out.println("No obstacles to save.");
            return;
        }

        String[] obstacleCoords = obstacleString.split(";");

        try (final PreparedStatement statement = connection.prepareStatement(
                "INSERT INTO obstacles (position, worldID) VALUES (?, ?)")) {

            for (String coord : obstacleCoords) {
                String trimmedCoord = coord.trim();
                if (!trimmedCoord.isEmpty()) {
                    statement.setString(1, trimmedCoord); // e.g., "1,2"
                    statement.setInt(2, worldID);
                    statement.executeUpdate();
                }
            }
        }
    }

    public  World restoreWorld(final Connection conn, int worldId) throws SQLException {
        World world = null;

        try {
            // Get world size
            PreparedStatement worldStmt = conn.prepareStatement("SELECT size FROM world WHERE id=?");
            worldStmt.setInt(1, worldId);
            ResultSet result = worldStmt.executeQuery();

            if (!result.next()) {
                System.out.println("World not found.");
                return null;
            }

            int size = result.getInt("size");
            result.close();
            worldStmt.close();

            // Prepare maze
            Maze maze = new Maze(" "); // avoid random generation
            ObstacleType type = ObstacleType.MOUNTAIN;

            // Get obstacles
            PreparedStatement stment = conn.prepareStatement("SELECT position FROM obstacles WHERE worldID=?");
            stment.setInt(1, worldId);
            ResultSet output = stment.executeQuery();

            while (output.next()) {
                String position = output.getString("position");  // e.g. "1,2;3,4"
                String[] parts = position.split(";");
                if (parts.length != 2) continue;

                String[] firstCoords = parts[0].split(",");
                String[] secondCoords = parts[1].split(",");

                int firstX = Integer.parseInt(firstCoords[0].trim());
                int firstY = Integer.parseInt(firstCoords[1].trim());
                int secX = Integer.parseInt(secondCoords[0].trim());
                int secY = Integer.parseInt(secondCoords[1].trim());

                maze.getObstacleList().add(new Obstacle(firstX, firstY, secX, secY, type));
            }

            output.close();
            stment.close();

            // Now create the World once
            boolean enableGUI = !java.awt.GraphicsEnvironment.isHeadless();
            world = new World(enableGUI, maze.toString());  // Use Maze directly if supported
            world.setWorldSize(size);           // Optional if not in constructor

        } catch (SQLException e) {
            System.err.println("Error restoring world: " + e.getMessage());
            return null;
        }

        return world;
    }

    public World restoreWorldByName(final Connection conn, String worldName) throws SQLException {
        PreparedStatement worldStmt = conn.prepareStatement("SELECT id, size FROM world WHERE name=?");
        worldStmt.setString(1, worldName);
        ResultSet result = worldStmt.executeQuery();

        if (!result.next()) {
            System.out.println("World '" + worldName + "' not found.");
            result.close();
            worldStmt.close();
            return null;
        }

        int worldId = result.getInt("id");
        int size = result.getInt("size");
        result.close();
        worldStmt.close();

        Maze maze = new Maze(" ");
        ObstacleType type = ObstacleType.MOUNTAIN;

        PreparedStatement stment = conn.prepareStatement("SELECT position FROM obstacles WHERE worldID=?");
        stment.setInt(1, worldId);
        ResultSet output = stment.executeQuery();

        while (output.next()) {
            String position = output.getString("position");
            String[] parts = position.split(";");
            if (parts.length != 2) continue;

            String[] firstCoords = parts[0].split(",");
            String[] secondCoords = parts[1].split(",");

            int firstX = Integer.parseInt(firstCoords[0].trim());
            int firstY = Integer.parseInt(firstCoords[1].trim());
            int secX = Integer.parseInt(secondCoords[0].trim());
            int secY = Integer.parseInt(secondCoords[1].trim());

            maze.getObstacleList().add(new Obstacle(firstX, firstY, secX, secY, type));
        }

        output.close();
        stment.close();

        boolean enableGUI = !java.awt.GraphicsEnvironment.isHeadless();
        World world = new World(enableGUI, maze.toString());
        world.setWorldSize(size);

        return world;
    }

    public void listWorlds() throws SQLException {
        Connection connection = getConnection();
        PreparedStatement stmt = connection.prepareStatement("SELECT id, name, size FROM world ORDER BY name");
        ResultSet rs = stmt.executeQuery();

        System.out.println("Available worlds:");
        boolean hasWorlds = false;
        while (rs.next()) {
            hasWorlds = true;
            int id = rs.getInt("id");
            String name = rs.getString("name");
            int size = rs.getInt("size");
            System.out.println(id + "\t" + (name != null ? name : "unnamed") + "\t" + size + "x" + size);
        }

        if (!hasWorlds) {
            System.out.println("No worlds found.");
        }

        rs.close();
        stmt.close();
        connection.close();
    }






//    private void updateData( final Connection connection , MultiServers server)
//            throws SQLException
//    {
//        try( final PreparedStatement stmt = connection.prepareStatement("UPDATE world SET size=? WHERE id= ?")){
//            stmt.setInt(1,server.getSize());
//            stmt.setInt();
//            boolean gotAResultSet = stmt.execute( // return value should be `false` as before
//                    "UPDATE products SET name = \"Sourflat IPA\" WHERE name = \"Lemondrop IPA\""
//            );
//            if( gotAResultSet ){
//                throw new RuntimeException( "Unexpectedly got a SQL resultset." );
//            }else{
//                final int updateCount = stmt.getUpdateCount();
//                if( updateCount == 1 ){
//                    System.out.println( "1 row UPDATED in products" );
//                }else{
//                    throw new RuntimeException( "Expected 1 row to be updated, but got " + updateCount );
//                }
//            }
//        }
//    }
//
//    private void deleteData( final Connection connection )
//            throws SQLException
//    {
//        try( final Statement stmt = connection.createStatement() ){
//            boolean gotAResultSet = stmt.execute( // return value should be `false` as before
//                    "DELETE FROM products WHERE id = 2"
//            );
//            if( gotAResultSet ){
//                throw new RuntimeException( "Unexpectedly got a SQL resultset." );
//            }else{
//                final int updateCount = stmt.getUpdateCount();
//                if( updateCount == 1 ){
//                    System.out.println( "1 row DELETED from products" );
//                }else{
//                    throw new RuntimeException( "Expected 1 row to be deleted, but got " + updateCount );
//                }
//            }
//        }
//    }

//    private void readData( final Connection connection )
//            throws SQLException
//    {
//        try( final PreparedStatement stmt = connection.createStatement() ){
//            boolean gotAResultSet = stmt.execute(
//                    "SELECT p.name productname, i.type, i.name ingredname, r.qty, r.units "
//                            + "FROM products p, recipe_quantities r, ingredients i "
//                            + "WHERE "
//                            + "        productname = 'Buffalo Bay Blonde'"
//                            + "    AND p.id = r.product_id "
//                            + "    AND r.ingredient_id = i.id"
//            );
//            if( ! gotAResultSet ){
//                throw new RuntimeException( "Expected a SQL resultset, but we got an update count instead!" );
//            }
//            try( ResultSet results = stmt.getResultSet() ){
//                int rowNo = 1;
//                while( results.next() ){
//                    final String productName = results.getString( "productname" );
//                    final String ingredType = results.getString( "type" );
//                    final String ingredName = results.getString( "ingredname" );
//                    final int qty = results.getInt( "qty" );
//                    final String units = results.getString( "units" );
//
//                    final StringBuilder b = new StringBuilder( "Row " ).append( rowNo ).append( SEPARATOR )
//                            .append( productName ).append( SEPARATOR )
//                            .append( ingredType ).append( SEPARATOR )
//                            .append( ingredName ).append( SEPARATOR )
//                            .append( qty ).append( SEPARATOR )
//                            .append( units );
//                    System.out.println( b.toString() );
//                }
//            }
//        }
//    }

//    private void processCmdLineArgs( String[] args ){
//        if( args.length == 2 && args[ 0 ].equals( "-f" )){
//            final File dbFile = new File( args[ 1 ] );
//            if( dbFile.exists() ){
//                dbUrl = DISK_DB_URL_PREFIX + args[ 1 ];
//            }else{
//                throw new IllegalArgumentException( "Database file " + dbFile.getName() + " not found." );
//            }
//        }else{
//            throw new RuntimeException( "Expected arguments '-f filenanme' but didn't find it." );
//        }
//    }
}