package za.co.wethinkcode.robots.Database;

import java.sql.*;

/**
 * Database initializer for Robot Worlds database schema
 * Handles creation and migration of database tables
 */
public class DatabaseInitializer {
    
    private static final String DB_URL = "jdbc:sqlite:world.db";
    
    /**
     * Initialize the database with proper schema
     */
    public static void initializeDatabase() throws SQLException {
        try (Connection connection = DriverManager.getConnection(DB_URL)) {
            createTables(connection);
            System.out.println("Database initialized successfully.");
        }
    }
    
    /**
     * Create all necessary tables if they don't exist
     */
    private static void createTables(Connection connection) throws SQLException {
        createWorldTable(connection);
        createObstaclesTable(connection);
    }
    
    /**
     * Create the world table with support for named worlds
     */
    private static void createWorldTable(Connection connection) throws SQLException {
        String createWorldTable = """
            CREATE TABLE IF NOT EXISTS world (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                size INTEGER NOT NULL,
                name TEXT UNIQUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """;
        
        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createWorldTable);
        }
    }
    
    /**
     * Create the obstacles table
     */
    private static void createObstaclesTable(Connection connection) throws SQLException {
        String createObstaclesTable = """
            CREATE TABLE IF NOT EXISTS obstacles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                worldID INTEGER NOT NULL,
                obstacle_type TEXT NOT NULL DEFAULT 'MOUNTAIN',
                top_left_x INTEGER NOT NULL,
                top_left_y INTEGER NOT NULL,
                bottom_right_x INTEGER NOT NULL,
                bottom_right_y INTEGER NOT NULL,
                FOREIGN KEY(worldID) REFERENCES world(id) ON DELETE CASCADE
            )
        """;
        
        try (Statement stmt = connection.createStatement()) {
            stmt.executeUpdate(createObstaclesTable);
        }
    }
    
    /**
     * Check if database needs migration from old schema
     */
    public static boolean needsMigration() throws SQLException {
        try (Connection connection = DriverManager.getConnection(DB_URL)) {
            // Check if world table exists but doesn't have name column
            DatabaseMetaData metaData = connection.getMetaData();
            
            // Check if world table exists
            try (ResultSet tables = metaData.getTables(null, null, "world", null)) {
                if (!tables.next()) {
                    return false; // No migration needed, fresh install
                }
            }
            
            // Check if name column exists
            try (ResultSet columns = metaData.getColumns(null, null, "world", "name")) {
                return !columns.next(); // Migration needed if name column doesn't exist
            }
        }
    }
    
    /**
     * Migrate from old schema to new schema
     */
    public static void migrateDatabase() throws SQLException {
        try (Connection connection = DriverManager.getConnection(DB_URL)) {
            // Add name column if it doesn't exist
            if (!hasColumn(connection, "world", "name")) {
                try (Statement stmt = connection.createStatement()) {
                    stmt.executeUpdate("ALTER TABLE world ADD COLUMN name TEXT UNIQUE");
                    stmt.executeUpdate("ALTER TABLE world ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP");
                    
                    // Give default names to existing worlds
                    stmt.executeUpdate("UPDATE world SET name = 'world_' || id WHERE name IS NULL");
                    
                    System.out.println("Database migrated to support named worlds.");
                }
            }
            
            // Migrate obstacles table if needed
            migrateObstaclesTable(connection);
        }
    }
    
    /**
     * Check if a table has a specific column
     */
    private static boolean hasColumn(Connection connection, String tableName, String columnName) throws SQLException {
        DatabaseMetaData metaData = connection.getMetaData();
        try (ResultSet columns = metaData.getColumns(null, null, tableName, columnName)) {
            return columns.next();
        }
    }
    
    /**
     * Migrate obstacles table to new format
     */
    private static void migrateObstaclesTable(Connection connection) throws SQLException {
        // Check if obstacles table needs migration (has position column instead of coordinate columns)
        if (hasColumn(connection, "obstacles", "position") && 
            !hasColumn(connection, "obstacles", "top_left_x")) {
            
            System.out.println("Migrating obstacles table...");
            
            // Create new obstacles table structure
            try (Statement stmt = connection.createStatement()) {
                // Rename old table
                stmt.executeUpdate("ALTER TABLE obstacles RENAME TO obstacles_old");
                
                // Create new table
                createObstaclesTable(connection);
                
                // Migrate data
                migrateObstacleData(connection);
                
                // Drop old table
                stmt.executeUpdate("DROP TABLE obstacles_old");
                
                System.out.println("Obstacles table migrated successfully.");
            }
        }
    }
    
    /**
     * Migrate obstacle data from old format to new format
     */
    private static void migrateObstacleData(Connection connection) throws SQLException {
        String selectOld = "SELECT worldID, position FROM obstacles_old";
        String insertNew = "INSERT INTO obstacles (worldID, obstacle_type, top_left_x, top_left_y, bottom_right_x, bottom_right_y) VALUES (?, ?, ?, ?, ?, ?)";
        
        try (PreparedStatement selectStmt = connection.prepareStatement(selectOld);
             PreparedStatement insertStmt = connection.prepareStatement(insertNew);
             ResultSet rs = selectStmt.executeQuery()) {
            
            while (rs.next()) {
                int worldID = rs.getInt("worldID");
                String position = rs.getString("position");
                
                // Parse old position format "x1,y1;x2,y2"
                if (position != null && position.contains(";")) {
                    String[] parts = position.split(";");
                    if (parts.length == 2) {
                        String[] coord1 = parts[0].split(",");
                        String[] coord2 = parts[1].split(",");
                        
                        if (coord1.length == 2 && coord2.length == 2) {
                            try {
                                int x1 = Integer.parseInt(coord1[0].trim());
                                int y1 = Integer.parseInt(coord1[1].trim());
                                int x2 = Integer.parseInt(coord2[0].trim());
                                int y2 = Integer.parseInt(coord2[1].trim());
                                
                                insertStmt.setInt(1, worldID);
                                insertStmt.setString(2, "MOUNTAIN"); // Default type
                                insertStmt.setInt(3, Math.min(x1, x2)); // top_left_x
                                insertStmt.setInt(4, Math.min(y1, y2)); // top_left_y
                                insertStmt.setInt(5, Math.max(x1, x2)); // bottom_right_x
                                insertStmt.setInt(6, Math.max(y1, y2)); // bottom_right_y
                                insertStmt.executeUpdate();
                            } catch (NumberFormatException e) {
                                System.err.println("Skipping invalid obstacle position: " + position);
                            }
                        }
                    }
                }
            }
        }
    }
}
