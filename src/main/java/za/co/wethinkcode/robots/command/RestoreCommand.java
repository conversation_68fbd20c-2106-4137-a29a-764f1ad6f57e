package za.co.wethinkcode.robots.command;

import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.Database.SaveDatabase;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.world.World;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class RestoreCommand extends Command{

    private static RestoreCommand instance;
    private int worldId;
    public RestoreCommand(){
        super("restore");
    }
    public static synchronized RestoreCommand getInstance() {
        if (instance == null) {
            instance = new RestoreCommand();
        }
        return instance;
    }
    public void setWorldId(int id) {
        this.worldId = id;
    }
    @Override
    public JsonObject execute(World world) {
        return null;
    }

    public void handleInput(String input) {
        String[] parts = input.trim().split("\\s+");
        if (parts.length < 2) {
            System.out.println("Usage: restore <worldname|worldid> or restore list");
            return;
        }

        String identifier = parts[1];

        try {
            if (identifier.equals("list")) {
                listWorlds();
            } else if (identifier.matches("\\d+")) {
                int id = Integer.parseInt(identifier);
                setWorldId(id);
                this.restore();
            } else {
                this.restoreByName(identifier);
            }
        } catch (NumberFormatException | SQLException e) {
            System.out.println("Error: " + e.getMessage());
        }
    }
    public void restore() throws SQLException {
        // write code that will take go into the database retrieve the name of the world and start the server
        System.out.println("Restoring ....");
        if (worldId <= 0) {
            System.out.println("Invalid or missing world ID.");
            return;
        }
        try (Connection conn = DriverManager.getConnection("jdbc:sqlite:world.db")) {
            SaveDatabase saveDatabase = new SaveDatabase();
            World restoredWorld = saveDatabase.restoreWorld(conn, worldId);

            if (restoredWorld != null) {
                MultiServers.getServer().setWorld(restoredWorld);
                System.out.println("World restored successfully.");
                // Build and return success JsonObject if needed
            } else {
                System.out.println("World not found.");
                // Build and return failure JsonObject if needed
            }
        } catch (SQLException e) {
            e.printStackTrace();
            // Build and return error JsonObject if needed
        }
        return;
    }

    public void restoreByName(String worldName) throws SQLException {
        Connection conn = DriverManager.getConnection("jdbc:sqlite:world.db");
        SaveDatabase saveDatabase = new SaveDatabase();
        World restoredWorld = saveDatabase.restoreWorldByName(conn, worldName);

        if (restoredWorld != null) {
            MultiServers.getServer().setWorld(restoredWorld);
            System.out.println("World '" + worldName + "' restored.");
        } else {
            System.out.println("World '" + worldName + "' not found.");
        }
        conn.close();
    }

    public void listWorlds() throws SQLException {
        SaveDatabase saveDatabase = new SaveDatabase();
        saveDatabase.listWorlds();
    }
    }


