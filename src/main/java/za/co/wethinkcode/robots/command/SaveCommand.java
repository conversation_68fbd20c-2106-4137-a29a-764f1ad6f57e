package za.co.wethinkcode.robots.command;

import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.Database.SaveDatabase;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.world.World;

import java.sql.SQLException;


public class SaveCommand extends Command{
    private static SaveCommand instance;

    public SaveCommand(){
        super("save");
    }
    public static synchronized SaveCommand getInstance() {
        if (instance == null) {
            instance = new SaveCommand();
        }
        return instance;
    }
    @Override
    public JsonObject execute(World world) {
        return null;
    }

    public void save(MultiServers server) throws SQLException {
        System.out.println("Saving world...");
        try {
            SaveDatabase saveDatabase = new SaveDatabase();
            saveDatabase.server = server;
            saveDatabase.createConnection();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public void saveWithName(MultiServers server, String worldName) throws SQLException {
        SaveDatabase saveDatabase = new SaveDatabase();
        saveDatabase.server = server;
        saveDatabase.saveWorldWithName(worldName);
    }

    public void handleInput(String input, MultiServers server) throws SQLException {
        String[] parts = input.trim().split("\\s+");

        if (parts.length == 1) {
            System.out.print("Enter world name: ");
            java.util.Scanner scanner = new java.util.Scanner(System.in);
            String worldName = scanner.nextLine().trim();

            if (!worldName.isEmpty()) {
                saveWithName(server, worldName);
            } else {
                System.out.println("Save cancelled.");
            }
        } else if (parts.length == 2) {
            String worldName = parts[1];
            saveWithName(server, worldName);
        } else {
            System.out.println("Usage: save [worldname]");
        }
    }


    }

