package za.co.wethinkcode.robots.command;

import com.google.gson.JsonObject;
import za.co.wethinkcode.robots.Database.SaveDatabase;
import za.co.wethinkcode.robots.server.MultiServers;
import za.co.wethinkcode.robots.world.World;

import java.sql.SQLException;


public class SaveCommand extends Command{
    private static SaveCommand instance;

    public SaveCommand(){
        super("save");
    }
    public static synchronized SaveCommand getInstance() {
        if (instance == null) {
            instance = new SaveCommand();
        }
        return instance;
    }
    @Override
    public JsonObject execute(World world) {
        return null;
    }

    public void save(MultiServers server) throws SQLException {
        System.out.println("Saving world...");
        try {
            SaveDatabase saveDatabase = new SaveDatabase();
            saveDatabase.server = server;
            saveDatabase.createConnection();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }


    }

